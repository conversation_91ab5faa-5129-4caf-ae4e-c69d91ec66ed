import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: root
    color: "#f5f5f5"
    
    RowLayout {
        anchors.fill: parent
        spacing: 0
        
        // 左侧边栏
        Rectangle {
            Layout.preferredWidth: 250
            Layout.fillHeight: true
            color: "#f0f0f0"
            border.color: "#e0e0e0"
            border.width: 1
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 10
                
                // 顶部按钮区域
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 10
                    
                    Button {
                        text: "配置"
                        flat: true
                        Layout.preferredWidth: 50
                    }
                    
                    Button {
                        text: "试验"
                        flat: true
                        Layout.preferredWidth: 50
                        background: Rectangle {
                            color: "#e8f5e8"
                            border.color: "#4CAF50"
                            border.width: 1
                            radius: 4
                        }
                    }
                    
                    Button {
                        text: "设置"
                        flat: true
                        Layout.preferredWidth: 50
                    }
                    
                    Item {
                        Layout.fillWidth: true
                    }
                    
                    Button {
                        text: "+"
                        Layout.preferredWidth: 30
                        Layout.preferredHeight: 30
                    }
                }
                
                // 新建聊天按钮
                Button {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 40
                    text: "+ 新建聊天"
                    
                    background: Rectangle {
                        color: parent.pressed ? "#e0e0e0" : "#f8f8f8"
                        border.color: "#d0d0d0"
                        border.width: 1
                        radius: 6
                    }
                }
                
                // 聊天列表
                ScrollView {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    
                    ListView {
                        model: ListModel {
                            ListElement { title: "默认话题" }
                            ListElement { title: "默认话题" }
                            ListElement { title: "默认话题" }
                        }
                        
                        delegate: Rectangle {
                            width: ListView.view.width
                            height: 50
                            color: "#f8f8f8"
                            border.color: "#e0e0e0"
                            border.width: 1
                            radius: 6
                            
                            Text {
                                anchors.left: parent.left
                                anchors.leftMargin: 15
                                anchors.verticalCenter: parent.verticalCenter
                                text: model.title
                                color: "#333"
                            }
                            
                            MouseArea {
                                anchors.fill: parent
                                hoverEnabled: true
                                onEntered: parent.color = "#e8e8e8"
                                onExited: parent.color = "#f8f8f8"
                            }
                        }
                        
                        spacing: 5
                    }
                }
            }
        }
        
        // 主聊天区域
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "white"
            
            ColumnLayout {
                anchors.fill: parent
                spacing: 0
                
                // 顶部标题栏
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 60
                    color: "white"
                    border.color: "#e0e0e0"
                    border.width: 1
                    
                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 15
                        
                        Image {
                            Layout.preferredWidth: 24
                            Layout.preferredHeight: 24
                            source: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiM0Q0FGNTASCZ8L2NpcmNsZT4KPHN2Zz4K"
                        }
                        
                        Text {
                            text: "DeepSeek Chat | 深度求索"
                            font.pixelSize: 16
                            font.weight: Font.Medium
                            color: "#333"
                        }
                        
                        Rectangle {
                            Layout.preferredWidth: 1
                            Layout.preferredHeight: 20
                            color: "#e0e0e0"
                        }
                        
                        Text {
                            text: "×"
                            font.pixelSize: 14
                            color: "#666"
                        }
                        
                        Item {
                            Layout.fillWidth: true
                        }
                        
                        Button {
                            Layout.preferredWidth: 30
                            Layout.preferredHeight: 30
                            text: "🔍"
                            flat: true
                        }
                        
                        Button {
                            Layout.preferredWidth: 30
                            Layout.preferredHeight: 30
                            text: "⚙"
                            flat: true
                        }
                    }
                }
                
                // 聊天内容区域
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: "white"
                    
                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 20
                        
                        Text {
                            Layout.alignment: Qt.AlignHCenter
                            text: "你好，我是DeepSeek，你的AI助手。你可以向我提问任何问题。"
                            font.pixelSize: 16
                            color: "#666"
                            horizontalAlignment: Text.AlignHCenter
                        }
                    }
                }
                
                // 底部输入区域
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 80
                    color: "white"
                    border.color: "#e0e0e0"
                    border.width: 1
                    
                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 15
                        spacing: 10
                        
                        // 输入框
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50
                            color: "white"
                            border.color: "#e0e0e0"
                            border.width: 1
                            radius: 8
                            
                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 10
                                spacing: 10
                                
                                ScrollView {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    
                                    TextArea {
                                        placeholderText: "请输入你的问题，按 Enter 发送"
                                        wrapMode: TextArea.Wrap
                                        selectByMouse: true
                                        font.pixelSize: 14
                                        background: Rectangle {
                                            color: "transparent"
                                        }
                                    }
                                }
                                
                                // 底部工具栏
                                RowLayout {
                                    Layout.alignment: Qt.AlignBottom
                                    spacing: 5
                                    
                                    Button {
                                        Layout.preferredWidth: 30
                                        Layout.preferredHeight: 30
                                        text: "📎"
                                        flat: true
                                        font.pixelSize: 16
                                    }
                                    
                                    Button {
                                        Layout.preferredWidth: 30
                                        Layout.preferredHeight: 30
                                        text: "📷"
                                        flat: true
                                        font.pixelSize: 16
                                    }
                                    
                                    Button {
                                        Layout.preferredWidth: 30
                                        Layout.preferredHeight: 30
                                        text: "🎤"
                                        flat: true
                                        font.pixelSize: 16
                                    }
                                    
                                    Button {
                                        Layout.preferredWidth: 30
                                        Layout.preferredHeight: 30
                                        text: "📋"
                                        flat: true
                                        font.pixelSize: 16
                                    }
                                    
                                    Button {
                                        Layout.preferredWidth: 30
                                        Layout.preferredHeight: 30
                                        text: "🎯"
                                        flat: true
                                        font.pixelSize: 16
                                    }
                                    
                                    Button {
                                        Layout.preferredWidth: 30
                                        Layout.preferredHeight: 30
                                        text: "😊"
                                        flat: true
                                        font.pixelSize: 16
                                    }
                                }
                            }
                        }
                        
                        // 发送按钮
                        Button {
                            Layout.preferredWidth: 60
                            Layout.preferredHeight: 50
                            text: "✈"
                            font.pixelSize: 18
                            
                            background: Rectangle {
                                color: parent.pressed ? "#45a049" : "#4CAF50"
                                radius: 8
                            }
                            
                            contentItem: Text {
                                text: parent.text
                                font: parent.font
                                color: "white"
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                    }
                }
            }
        }
    }
}
